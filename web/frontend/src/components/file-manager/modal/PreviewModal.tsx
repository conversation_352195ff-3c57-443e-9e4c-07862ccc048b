import { useState, useEffect, useRef } from 'react';
import {
  HiOutlineXCircle,
  HiOutlineDocumentText,
  HiOutlineClock,
  HiOutlineInformationCircle,
  HiDownload,
  HiVolumeUp,
  HiVolumeOff,
  HiPlay,
  HiPause,
  HiOutlineFilm
} from 'react-icons/hi';
import { ConvertItem } from '../../../types/files';
import ReactPlayer from 'react-player';
import { formatBytes, formatDate } from '../../../utils/fileUtils';
import { downloadFile } from '../../../api/filesApi';
import './ModernPreviewModal.css';

interface PreviewModalProps {
  previewFile: ConvertItem | null;
  isOpen: boolean;
  onClose: () => void;
}

const PreviewModal = ({ previewFile, isOpen, onClose }: PreviewModalProps) => {
  const [isPlayerLoading, setIsPlayerLoading] = useState<boolean>(true);
  const [playerError, setPlayerError] = useState<boolean>(false);
  const [previewURL, setPreviewURL] = useState<string>("");
  const [isPlaying, setIsPlaying] = useState<boolean>(true);
  const [isMuted, setIsMuted] = useState<boolean>(false);
  const [progress, setProgress] = useState<number>(0);
  const [duration, setDuration] = useState<number>(0);
  const playerRef = useRef<ReactPlayer>(null);

  useEffect(() => {
    if (previewFile) {
      setPlayerError(false);
      setIsPlayerLoading(true);
      setIsPlaying(true);
      setProgress(0);

      // Fetch preview URL from backend API
      const fetchPreviewURL = async () => {
        try {
          console.log('Fetching preview URL for file ID:', previewFile.id);
          const response = await fetch(`/api/v1/files/${previewFile.id}/preview-url`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
            },
          });

          if (!response.ok) {
            throw new Error(`Failed to get preview URL: ${response.status}`);
          }

          const data = await response.json();
          console.log('Preview URL response:', data);

          if (data.previewUrl) {
            setPreviewURL(data.previewUrl);
            console.log('Using Backblaze URL for preview:', data.previewUrl);
          } else {
            throw new Error('No preview URL returned from server');
          }
        } catch (error) {
          console.error('Failed to get preview URL:', error);
          // Fallback to local path for legacy files
          const fallbackUrl = `/data${previewFile.location}${previewFile.filename}`;
          setPreviewURL(fallbackUrl);
          console.log('Using fallback local path for preview:', fallbackUrl);
        }
      };

      fetchPreviewURL();
    }
  }, [previewFile]);

  const handlePlayerPlay = () => {
    setIsPlayerLoading(false);
    setIsPlaying(true);
  };

  const handlePlayerPause = () => {
    setIsPlaying(false);
  };

  const handlePlayerError = () => {
    setIsPlayerLoading(false);
    setPlayerError(true);
  };

  const handleProgress = (state: { played: number; playedSeconds: number; loaded: number; loadedSeconds: number }) => {
    setProgress(state.playedSeconds);
  };

  const handleDuration = (duration: number) => {
    setDuration(duration);
  };

  const togglePlay = () => {
    setIsPlaying(!isPlaying);
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  const handleDownload = () => {
    if (previewFile) {
      downloadFile(previewFile.id);
    }
  };

  // Format time in MM:SS format
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };

  if (!previewFile) return null;
  if (!isOpen) return null;

  return (
    <div className="modern-preview-overlay">
      <div className="modern-preview-container">
        <div className="modern-player-wrapper">
          {isPlayerLoading && (
            <div className="modern-loading-overlay">
              <div className="modern-loading-spinner"></div>
              <div className="modern-loading-text">Loading video...</div>
            </div>
          )}
          <div className="modern-player-container">
            {playerError ? (
              <div className="modern-error-container">
                <div className="modern-error-content">
                  <HiOutlineXCircle className="modern-error-icon" />
                  <p className="modern-error-message">
                    Failed to load video. The file may be corrupted or in an unsupported format.
                  </p>
                </div>
              </div>
            ) : (
              <ReactPlayer
                ref={playerRef}
                width="100%"
                height="100%"
                url={previewURL}
                playing={isPlaying}
                controls={true}
                volume={0.5}
                muted={isMuted}
                onPlay={handlePlayerPlay}
                onPause={handlePlayerPause}
                onError={handlePlayerError}
                onProgress={handleProgress}
                onDuration={handleDuration}
                config={{
                  file: {
                    attributes: {
                      controlsList: 'nodownload',
                      disablePictureInPicture: true,
                    },
                    forceVideo: true,
                  }
                }}
                style={{ backgroundColor: '#000' }}
              />
            )}
          </div>
          <div
            onClick={onClose}
            className="video-close-btn"
            title="Close"
          >
            <HiOutlineXCircle className="video-close-icon" />
          </div>
        </div>

        <div className="modern-file-info">
          <div className="modern-file-header">
            <h3 className="modern-file-name">{previewFile.name || previewFile.filename}</h3>
            <div className="modern-media-controls">
              <button
                className="modern-media-button"
                onClick={togglePlay}
                title={isPlaying ? "Pause" : "Play"}
              >
                {isPlaying ? <HiPause className="modern-media-icon" /> : <HiPlay className="modern-media-icon" />}
                <span>{isPlaying ? "Pause" : "Play"}</span>
              </button>
              <button
                className="modern-media-button"
                onClick={toggleMute}
                title={isMuted ? "Unmute" : "Mute"}
              >
                {isMuted ?
                  <HiVolumeOff className="modern-media-icon" /> :
                  <HiVolumeUp className="modern-media-icon" />
                }
                <span>{isMuted ? "Unmute" : "Mute"}</span>
              </button>
              <button
                className="modern-media-button"
                onClick={handleDownload}
                title="Download"
              >
                <HiDownload className="modern-media-icon" />
                <span>Download</span>
              </button>
            </div>
          </div>

          <div className="modern-file-details">
            <div className="modern-file-detail" title="File Size">
              <HiOutlineDocumentText className="modern-detail-icon" />
              <span className="modern-detail-text">{formatBytes(previewFile.size)}</span>
            </div>
            <div className="modern-file-detail" title="Created Date">
              <HiOutlineClock className="modern-detail-icon" />
              <span className="modern-detail-text">{formatDate(previewFile.created_at)}</span>
            </div>
            <div className="modern-file-detail" title="File Location">
              <HiOutlineInformationCircle className="modern-detail-icon" />
              <span className="modern-detail-text">{previewFile.location}</span>
            </div>
            <div className="modern-file-detail" title="Video Duration">
              <HiOutlineFilm className="modern-detail-icon" />
              <span className="modern-detail-text">
                {duration > 0 ? `${formatTime(progress)} / ${formatTime(duration)}` : 'Loading duration...'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PreviewModal;
