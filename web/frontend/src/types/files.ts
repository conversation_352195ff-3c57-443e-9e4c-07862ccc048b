// FileStatusUpdate represents a status update for a file
export interface FileStatusUpdate {
  id: string; // Changed to string for MongoDB ObjectID
  status: number;
}

// ConvertItem represents a file in the system
export interface ConvertItem {
  id: string; // Changed to string for MongoDB ObjectID
  filename: string;
  location: string;
  duration: number;
  status: number;
  size: number;
  storage_type: number;
  created_at: string;
  updated_at: string;
  c_location: string;
  name: string;
  description: string;
  episode: string;
  width?: number;
  height?: number;
  fps?: number;
  video_codec?: string;
  audio_codec?: string;
  bitrate?: number;
  recorder_id?: number;
  codec_settings_version?: number;
  // New fields for Backblaze B2 integration
  backblaze_file_id?: string;
  backblaze_url?: string;
}

// ConvertItemUpdateInput represents the input for updating a convert item
export interface ConvertItemUpdateInput {
  name: string;
  description: string;
  episode: string;
}

// ConvertItemListResult represents the result of listing convert items
export interface ConvertItemListResult {
  items: ConvertItem[];
  total_items: number;
  total_pages: number;
  page: number;
  limit: number;
}

// FileStatus represents the status of a file
export enum FileStatus {
  Success = 0,
  Queue = 1,
  Failed = 2,
  Transcoding = 3,
}

// StorageType represents the type of storage
export enum StorageType {
  Local = 0,
  S3 = 1,
}

export interface BucketInfo {
  name: string;
  id: string;
  created_at: string;
}

export interface FileInfo {
  id: string;
  name: string;
  size: number;
  content_type: string;
  uploaded_at: string;
  bucket_id: string;
  url: string;
}
