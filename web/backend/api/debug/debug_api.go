package debug

import (
	"context"
	"net/http"
	"showfer-web/config"
	"showfer-web/repository"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DebugAPI handles debug endpoints
type DebugAPI struct {
	mongoRepo *repository.MongoConvertItemsRepository
}

// NewDebugAPI creates a new DebugAPI instance
func NewDebugAPI() *DebugAPI {
	return &DebugAPI{
		mongoRepo: repository.NewMongoConvertItemsRepository(),
	}
}

// GetRecentUploads handles GET /api/v1/debug/recent-uploads
func (api *DebugAPI) GetRecentUploads(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get MongoDB database
	mongoDatabase := config.GetMongoDatabase()
	collection := mongoDatabase.Collection("convert_items")

	// Find recent uploads (last 10)
	opts := options.Find().SetSort(bson.D{{Key: "createdAt", Value: -1}}).SetLimit(10)
	cursor, err := collection.Find(ctx, bson.M{}, opts)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to query MongoDB", "details": err.Error()})
		return
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err = cursor.All(ctx, &results); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to decode results", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"count":   len(results),
		"uploads": results,
	})
}

// GetUploadsByBucket handles GET /api/v1/debug/uploads-by-bucket/:bucketId
func (api *DebugAPI) GetUploadsByBucket(c *gin.Context) {
	bucketIdStr := c.Param("bucketId")
	bucketId, err := primitive.ObjectIDFromHex(bucketIdStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid bucket ID", "details": err.Error()})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get MongoDB database
	mongoDatabase := config.GetMongoDatabase()
	collection := mongoDatabase.Collection("convert_items")

	// Find uploads by bucket
	filter := bson.M{"bucket": bson.M{"$in": []primitive.ObjectID{bucketId}}}
	opts := options.Find().SetSort(bson.D{{Key: "createdAt", Value: -1}})
	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to query MongoDB", "details": err.Error()})
		return
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err = cursor.All(ctx, &results); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to decode results", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":  true,
		"bucketId": bucketIdStr,
		"count":    len(results),
		"uploads":  results,
	})
}

// GetCollectionStats handles GET /api/v1/debug/collection-stats
func (api *DebugAPI) GetCollectionStats(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get MongoDB database
	mongoDatabase := config.GetMongoDatabase()
	collection := mongoDatabase.Collection("convert_items")

	// Get total count
	totalCount, err := collection.CountDocuments(ctx, bson.M{})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count documents", "details": err.Error()})
		return
	}

	// Get count by status
	pipeline := []bson.M{
		{"$group": bson.M{
			"_id":   "$status",
			"count": bson.M{"$sum": 1},
		}},
	}

	cursor, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to aggregate", "details": err.Error()})
		return
	}
	defer cursor.Close(ctx)

	var statusCounts []bson.M
	if err = cursor.All(ctx, &statusCounts); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to decode aggregation", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":       true,
		"collection":    "convert_items",
		"total_count":   totalCount,
		"status_counts": statusCounts,
	})
}

// SearchUploads handles GET /api/v1/debug/search?filename=xxx
func (api *DebugAPI) SearchUploads(c *gin.Context) {
	filename := c.Query("filename")
	if filename == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "filename parameter is required"})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get MongoDB database
	mongoDatabase := config.GetMongoDatabase()
	collection := mongoDatabase.Collection("convert_items")

	// Search by filename (case-insensitive)
	filter := bson.M{"fileName": bson.M{"$regex": filename, "$options": "i"}}
	opts := options.Find().SetSort(bson.D{{Key: "createdAt", Value: -1}})
	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to search MongoDB", "details": err.Error()})
		return
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err = cursor.All(ctx, &results); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to decode results", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":  true,
		"filename": filename,
		"count":    len(results),
		"uploads":  results,
	})
}
