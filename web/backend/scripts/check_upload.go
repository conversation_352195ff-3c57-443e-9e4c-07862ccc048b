package main

import (
	"context"
	"fmt"
	"log"
	"showfer-web/config"
	"showfer-web/service/storage"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func main() {
	fmt.Println("=== Upload Check Script ===")

	// Check MongoDB
	fmt.Println("\n1. Checking MongoDB...")
	checkMongoDB()

	// Check Backblaze B2
	fmt.Println("\n2. Checking Backblaze B2...")
	checkBackblaze()
}

func checkMongoDB() {
	// Get MongoDB database
	mongoDatabase := config.GetMongoDatabase()
	collection := mongoDatabase.Collection("convert_items")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get total count
	totalCount, err := collection.CountDocuments(ctx, bson.M{})
	if err != nil {
		log.Printf("❌ Failed to count MongoDB documents: %v", err)
		return
	}

	fmt.Printf("📊 Total documents in convert_items: %d\n", totalCount)

	if totalCount == 0 {
		fmt.Println("⚠️  No documents found in convert_items collection")
		return
	}

	// Get recent uploads
	opts := options.Find().SetSort(bson.D{{Key: "createdAt", Value: -1}}).SetLimit(5)
	cursor, err := collection.Find(ctx, bson.M{}, opts)
	if err != nil {
		log.Printf("❌ Failed to query recent uploads: %v", err)
		return
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err = cursor.All(ctx, &results); err != nil {
		log.Printf("❌ Failed to decode results: %v", err)
		return
	}

	fmt.Printf("📋 Recent uploads (%d):\n", len(results))
	for i, result := range results {
		fmt.Printf("  %d. File: %v, Status: %v, Created: %v\n", 
			i+1, 
			result["fileName"], 
			result["status"], 
			result["createdAt"])
		
		if backblazeID, ok := result["BackblazeFileID"]; ok && backblazeID != "" {
			fmt.Printf("     Backblaze ID: %v\n", backblazeID)
		}
		if backblazeURL, ok := result["BackblazeURL"]; ok && backblazeURL != "" {
			fmt.Printf("     Backblaze URL: %v\n", backblazeURL)
		}
	}
}

func checkBackblaze() {
	// Get Backblaze credentials
	cfg, err := config.LoadBackblazeConfig()
	if err != nil {
		log.Printf("❌ Failed to load Backblaze config: %v", err)
		return
	}

	// Initialize Backblaze service
	backblazeService, err := storage.NewBackblazeService(cfg.KeyID, cfg.AppKey)
	if err != nil {
		log.Printf("❌ Failed to initialize Backblaze service: %v", err)
		return
	}

	// List buckets
	buckets, err := backblazeService.ListBuckets()
	if err != nil {
		log.Printf("❌ Failed to list Backblaze buckets: %v", err)
		return
	}

	fmt.Printf("🪣 Available Backblaze buckets (%d):\n", len(buckets))
	for i, bucket := range buckets {
		fmt.Printf("  %d. %s (ID: %s)\n", i+1, bucket.Name, bucket.ID)
	}

	// Check files in each bucket (limited to first few)
	for i, bucket := range buckets {
		if i >= 2 { // Only check first 2 buckets to avoid too much output
			break
		}
		
		fmt.Printf("\n📁 Files in bucket '%s':\n", bucket.Name)
		files, err := backblazeService.ListFiles(bucket.Name, "", 10) // List up to 10 files
		if err != nil {
			log.Printf("❌ Failed to list files in bucket %s: %v", bucket.Name, err)
			continue
		}

		if len(files) == 0 {
			fmt.Printf("  (empty)\n")
		} else {
			for j, file := range files {
				fmt.Printf("  %d. %s (Size: %d bytes)\n", j+1, file.FileName, file.Size)
			}
		}
	}
}
