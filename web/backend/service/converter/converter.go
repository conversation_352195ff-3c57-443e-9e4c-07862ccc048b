package converter

import (
	"bytes"
	"fmt"
	"os/exec"
	"showfer-web/service/logger"
	"strconv"
	"strings"
)

// GetDuration gets the duration of a video file using ffprobe
func GetDuration(filePath string) (float64, error) {
	cmd := exec.Command(
		"ffprobe",
		"-v", "error",
		"-show_entries", "format=duration",
		"-of", "default=noprint_wrappers=1:nokey=1",
		filePath,
	)

	var out bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &out

	err := cmd.Run()
	if err != nil {
		logger.Error("Failed to run ffprobe: %v\nOutput: %s", err, out.String())
		return 0, err
	}

	lines := strings.Split(out.String(), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}
		duration, err := strconv.ParseFloat(line, 64)
		if err == nil {
			return duration, nil
		}
	}

	logger.Error("Failed to parse any float from ffprobe output: %s", out.String())

	return 0, fmt.<PERSON><PERSON><PERSON>("no valid float found in ffprobe output")
}

// GetVideoCodec gets the video codec of a video file using ffprobe
func GetVideoCodec(filePath string) (string, error) {
	cmd := exec.Command(
		"ffprobe",
		"-v", "error",
		"-select_streams", "v:0",
		"-show_entries", "stream=codec_name",
		"-of", "default=noprint_wrappers=1:nokey=1",
		filePath,
	)

	var out bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &out

	err := cmd.Run()
	if err != nil {
		logger.Error("Failed to run ffprobe for video codec: %v\nOutput: %s", err, out.String())
		return "", err
	}

	codec := strings.TrimSpace(out.String())
	if codec == "" {
		return "", fmt.Errorf("no video codec found")
	}

	return codec, nil
}

// GetAudioCodec gets the audio codec of a video file using ffprobe
func GetAudioCodec(filePath string) (string, error) {
	cmd := exec.Command(
		"ffprobe",
		"-v", "error",
		"-select_streams", "a:0",
		"-show_entries", "stream=codec_name",
		"-of", "default=noprint_wrappers=1:nokey=1",
		filePath,
	)

	var out bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &out

	err := cmd.Run()
	if err != nil {
		logger.Error("Failed to run ffprobe for audio codec: %v\nOutput: %s", err, out.String())
		return "", err
	}

	codec := strings.TrimSpace(out.String())
	if codec == "" {
		return "", fmt.Errorf("no audio codec found")
	}

	return codec, nil
}
