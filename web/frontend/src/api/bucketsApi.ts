import api from './axios';

export interface Bucket {
  _id: string;
  title: string;
  bucketName: string;
  isShowAll: boolean;
  default: boolean;
  user: string[];
  createdAt: string;
  updatedAt: string;
  dateCache?: string;
}

// Get all buckets
export const getBuckets = async (): Promise<Bucket[]> => {
  const response = await api.get<Bucket[]>('/buckets');
  return response.data;
};

// Get bucket by ID
export const getBucketById = async (id: string): Promise<Bucket> => {
  const response = await api.get<Bucket>(`/buckets/${id}`);
  return response.data;
};

// Create a new bucket
export const createBucket = async (bucket: Omit<Bucket, '_id' | 'createdAt' | 'updatedAt'>): Promise<Bucket> => {
  const response = await api.post<Bucket>('/buckets', bucket);
  return response.data;
};

// Update bucket
export const updateBucket = async (id: string, bucket: Partial<Bucket>): Promise<Bucket> => {
  const response = await api.put<Bucket>(`/buckets/${id}`, bucket);
  return response.data;
};

// Delete bucket
export const deleteBucket = async (id: string): Promise<void> => {
  await api.delete(`/buckets/${id}`);
};
