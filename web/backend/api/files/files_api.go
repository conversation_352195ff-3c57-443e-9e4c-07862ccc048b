package files

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"showfer-web/config"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/logger"
	"showfer-web/service/storage"
	"showfer-web/service/uploader"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// FilesAPI handles file-related API endpoints
type FilesAPI struct {
	db                *sql.DB
	filesRepo         *repository.FilesRepository
	codecSettingsRepo *repository.CodecSettingsRepository
	baseDir           string
}

// NewFilesAPI creates a new FilesAPI
func NewFilesAPI(db *sql.DB, baseDir string) *FilesAPI {
	// Create a data directory if it doesn't exist
	if _, err := os.Stat(baseDir); os.IsNotExist(err) {
		err = os.MkdirAll(baseDir, 0755)
		if err != nil {
			logger.Error("Failed to create data directory: %v", err)
			panic(err)
		}
	}

	return &FilesAPI{
		db:                db,
		filesRepo:         repository.NewFilesRepository(db),
		codecSettingsRepo: repository.NewCodecSettingsRepository(db),
		baseDir:           baseDir,
	}
}

// GetFiles handles GET /api/v1/files
func (api *FilesAPI) GetFiles(c *gin.Context) {
	// Parse pagination parameters
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.DefaultQuery("limit", "50"))
	if err != nil || limit < 1 || limit > 100 {
		limit = 50
	}

	pagination := models.Pagination{
		Page:  page,
		Limit: limit,
	}

	// Get files with pagination
	result, err := api.filesRepo.ListConvertItems(pagination)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get files", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetFilesByLocation handles GET /api/v1/files/location/:location
func (api *FilesAPI) GetFilesByLocation(c *gin.Context) {
	location := c.Param("location")

	// Handle special "root" location
	if location == "root" {
		location = "/"
	} else {
		// Replace underscores with slashes for nested paths
		location = strings.ReplaceAll(location, "_", "/")

		// Handle case where we might have double slashes
		for strings.Contains(location, "//") {
			location = strings.ReplaceAll(location, "//", "/")
		}

		// Ensure location starts with /
		if !strings.HasPrefix(location, "/") {
			location = "/" + location
		}

		// Ensure location ends with /
		if !strings.HasSuffix(location, "/") && location != "/" {
			location = location + "/"
		}
	}

	// Get files by location
	files, err := api.filesRepo.ConvertItemsByFolder(location)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get files by location", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, files)
}

// GetFoldersByLocation handles GET /api/v1/files/folders/:location
func (api *FilesAPI) GetFoldersByLocation(c *gin.Context) {
	location := c.Param("location")

	// Handle special "root" location
	if location == "root" {
		location = "/"
	} else {
		// Replace underscores with slashes for nested paths
		location = strings.ReplaceAll(location, "_", "/")

		// Handle case where we might have double slashes
		for strings.Contains(location, "//") {
			location = strings.ReplaceAll(location, "//", "/")
		}

		// Ensure location starts with /
		if !strings.HasPrefix(location, "/") {
			location = "/" + location
		}

		// Ensure location ends with /
		if !strings.HasSuffix(location, "/") && location != "/" {
			location = location + "/"
		}
	}

	// Get folders by location
	folders, err := api.filesRepo.GetSubfolders(location)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get folders by location", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, folders)
}

// UploadFiles handles POST /api/v1/files/upload
func (api *FilesAPI) UploadFiles(c *gin.Context) {
	// Get location from form
	log.Println("This is an info message##################################")
	location := c.PostForm("location")
	if location == "" {
		location = "/"
	}

	// Get bucket ID from form (required for Backblaze upload)
	bucketIdStr := c.PostForm("bucketId")
	log.Println("bucketId", bucketIdStr)
	if bucketIdStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Bucket ID is required"})
		return
	}

	// Convert bucket ID string to ObjectID
	bucketID, err := primitive.ObjectIDFromHex(bucketIdStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid bucket ID format", "details": err.Error()})
		return
	}

	// Ensure location starts with /
	if !strings.HasPrefix(location, "/") {
		location = "/" + location
	}

	// Ensure location ends with /
	if !strings.HasSuffix(location, "/") && location != "/" {
		location = location + "/"
	}

	// Create temporary folder for processing
	tempDir := filepath.Join(api.baseDir, "temp")
	if _, err := os.Stat(tempDir); os.IsNotExist(err) {
		err = os.MkdirAll(tempDir, 0755)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create temp folder", "details": err.Error()})
			return
		}
	}

	// Get file from form
	form, err := c.MultipartForm()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse form", "details": err.Error()})
		return
	}

	files := form.File["files"]
	if len(files) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No files uploaded"})
		return
	}

	// Get Backblaze credentials from config
	cfg, err := config.LoadBackblazeConfig()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to load Backblaze config", "details": err.Error()})
		return
	}

	// Get bucket information from MongoDB using bucket ID
	mongoDatabase := config.GetMongoDatabase()
	bucketRepo := repository.NewBucketRepository(mongoDatabase)
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	bucket, err := bucketRepo.GetBucketByID(ctx, bucketID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid bucket ID in database", "details": err.Error()})
		return
	}

	bucketName := bucket.BucketName

	// Initialize Backblaze uploader
	backblazeService, err := storage.NewBackblazeService(cfg.KeyID, cfg.AppKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to initialize Backblaze service", "details": err.Error()})
		return
	}

	// Verify bucket exists in Backblaze B2 before proceeding
	logger.Log("Verifying bucket '%s' exists in Backblaze B2...", bucketName)
	b2Buckets, err := backblazeService.ListBuckets()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list Backblaze buckets", "details": err.Error()})
		return
	}

	bucketExists := false
	for _, b2Bucket := range b2Buckets {
		if b2Bucket.Name == bucketName {
			bucketExists = true
			break
		}
	}

	if !bucketExists {
		logger.Error("Bucket '%s' not found in Backblaze B2. Available buckets: %v", bucketName, func() []string {
			var names []string
			for _, b := range b2Buckets {
				names = append(names, b.Name)
			}
			return names
		}())
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Bucket not found in Backblaze B2",
			"details": fmt.Sprintf("Bucket '%s' does not exist in your Backblaze account", bucketName),
			"available_buckets": func() []string {
				var names []string
				for _, b := range b2Buckets {
					names = append(names, b.Name)
				}
				return names
			}(),
		})
		return
	}

	backblazeUploader := uploader.NewBackblazeUploader(backblazeService)

	var uploadedFiles []models.ConvertItemResponse
	for _, file := range files {
		// Save file to temporary location first
		filename := filepath.Base(file.Filename)
		tempFilePath := filepath.Join(tempDir, filename)

		// Check if file already exists in temp
		if _, err := os.Stat(tempFilePath); err == nil {
			// File exists, append timestamp to filename
			ext := filepath.Ext(filename)
			baseName := filename[:len(filename)-len(ext)]
			timestamp := time.Now().Format("**************")
			filename = fmt.Sprintf("%s_%s%s", baseName, timestamp, ext)
			tempFilePath = filepath.Join(tempDir, filename)
		}

		b2Buckets, err := backblazeService.ListBuckets()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list Backblaze buckets", "details": err.Error()})
			return
		}

		// Log all Backblaze bucket names
		var bucketNames []string
		for _, b := range b2Buckets {
			bucketNames = append(bucketNames, b.Name)
		}
		log.Printf("Backblaze B2 Buckets: %v", bucketNames)

		// Save file to temp location
		if err := c.SaveUploadedFile(file, tempFilePath); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save file", "details": err.Error()})
			return
		}

		// Process file and upload to Backblaze
		convertItem, err := backblazeUploader.ProcessFileToBackblaze(tempFilePath, location, bucketName, bucket.ID)
		if err != nil {
			// Log error but continue with other files
			logger.Error("Failed to process file %s: %v", filename, err)
			// Clean up temp file
			os.Remove(tempFilePath)
			continue
		}

		uploadedFiles = append(uploadedFiles, convertItem.ToResponse())
	}

	c.JSON(http.StatusOK, gin.H{
		"message": fmt.Sprintf("Successfully uploaded %d files", len(uploadedFiles)),
		"files":   uploadedFiles,
	})
}

// DeleteFile handles DELETE /api/v1/files/:id
func (api *FilesAPI) DeleteFile(c *gin.Context) {
	// Parse file ID
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file ID"})
		return
	}

	// Get file by ID
	file, err := api.filesRepo.GetConvertItemById(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get file", "details": err.Error()})
		}
		return
	}

	// Delete file
	filePath := filepath.Join(file.Location, file.Filename)
	err = api.filesRepo.DeleteFile(id, filePath, api.baseDir)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete file", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "File deleted successfully"})
}

// UpdateFile handles PUT /api/v1/files/:id
func (api *FilesAPI) UpdateFile(c *gin.Context) {
	// Parse file ID
	log.Println("This is an info message##################################")

	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file ID"})
		return
	}

	// Parse request body
	var input models.ConvertItemUpdateInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Get file by ID
	file, err := api.filesRepo.GetConvertItemById(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get file", "details": err.Error()})
		}
		return
	}

	// Update file
	file.Name = input.Name
	file.Description = input.Description
	file.Episode = input.Episode

	err = api.filesRepo.UpdateConvertItem(file)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update file", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, file)
}

// RenameFile handles PUT /api/v1/files/:id/rename
func (api *FilesAPI) RenameFile(c *gin.Context) {
	// Parse file ID
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file ID"})
		return
	}

	// Parse request body
	var input struct {
		NewFilename string `json:"new_filename"`
	}
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Validate new filename
	if input.NewFilename == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "New filename cannot be empty"})
		return
	}

	// Get file by ID
	file, err := api.filesRepo.GetConvertItemById(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get file", "details": err.Error()})
		}
		return
	}

	// Rename file on disk and update database
	err = api.filesRepo.RenameFile(file, input.NewFilename, api.baseDir)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to rename file", "details": err.Error()})
		return
	}

	// Get updated file
	updatedFile, err := api.filesRepo.GetConvertItemById(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get updated file", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedFile)
}

// DownloadFile handles GET /api/v1/files/:id/download
func (api *FilesAPI) DownloadFile(c *gin.Context) {
	// Parse file ID
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file ID"})
		return
	}

	// Get file by ID
	file, err := api.filesRepo.GetConvertItemById(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get file", "details": err.Error()})
		}
		return
	}

	// Construct file path
	filePath := filepath.Join(api.baseDir, file.Location, file.Filename)

	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, gin.H{"error": "File not found on disk"})
		return
	}

	// Set appropriate headers for file download
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", file.Filename))
	c.Header("Content-Type", "application/octet-stream")
	c.File(filePath)
}

// MoveFiles handles POST /api/v1/files/move
func (api *FilesAPI) MoveFiles(c *gin.Context) {
	// Parse request body
	var input struct {
		FileIDs     []int64 `json:"file_ids"`
		Destination string  `json:"destination"`
	}
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Validate input
	if len(input.FileIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No file IDs provided"})
		return
	}

	// Ensure destination starts with /
	if !strings.HasPrefix(input.Destination, "/") {
		input.Destination = "/" + input.Destination
	}

	// Ensure destination ends with /
	if !strings.HasSuffix(input.Destination, "/") && input.Destination != "/" {
		input.Destination = input.Destination + "/"
	}

	// Create destination folder if it doesn't exist
	destPath := filepath.Join(api.baseDir, input.Destination)
	if _, err := os.Stat(destPath); os.IsNotExist(err) {
		err = os.MkdirAll(destPath, 0755)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create destination folder", "details": err.Error()})
			return
		}
	}

	// Process each file
	failedCount := 0
	errors := []string{}
	successCount := 0

	for _, fileID := range input.FileIDs {
		// Get file by ID
		file, err := api.filesRepo.GetConvertItemById(fileID)
		if err != nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("Failed to get file with ID %d: %v", fileID, err))
			continue
		}

		// Skip files that are being processed
		if file.Status == int(models.FileStatusQueue) || file.Status == int(models.FileStatusProcessing) {
			failedCount++
			errors = append(errors, fmt.Sprintf("File %s is currently being processed and cannot be moved", file.Filename))
			continue
		}

		// Check if source and destination are the same
		if file.Location == input.Destination {
			failedCount++
			errors = append(errors, fmt.Sprintf("File %s is already in the destination folder", file.Filename))
			continue
		}

		// Check if a file with the same name already exists in the destination
		destFilePath := filepath.Join(destPath, file.Filename)
		if _, err := os.Stat(destFilePath); err == nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("A file with the name %s already exists in the destination folder", file.Filename))
			continue
		}

		// Move file on disk
		sourcePath := filepath.Join(api.baseDir, file.Location, file.Filename)
		err = os.Rename(sourcePath, destFilePath)
		if err != nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("Failed to move file %s: %v", file.Filename, err))
			continue
		}

		// Update file location in database
		oldLocation := file.Location

		// Use the new UpdateFileLocation function to update both location and c_location
		// and also update any references in schedules
		err = api.filesRepo.UpdateFileLocation(file.ID, oldLocation, input.Destination)
		if err != nil {
			// Try to move the file back to its original location
			os.Rename(destFilePath, sourcePath)

			failedCount++
			errors = append(errors, fmt.Sprintf("Failed to update file %s in database: %v", file.Filename, err))
			continue
		}

		// Update the in-memory file object to reflect the new location
		file.Location = input.Destination
		file.CLocation = input.Destination

		successCount++
		logger.Log("Moved file %s from %s to %s", file.Filename, oldLocation, input.Destination)
	}

	c.JSON(http.StatusOK, gin.H{
		"success":      failedCount == 0,
		"failedCount":  failedCount,
		"successCount": successCount,
		"errors":       errors,
	})
}

// GetFileInfoById handles GET /api/v1/files/:id
func (api *FilesAPI) GetFileInfoById(c *gin.Context) {
	// Parse file ID
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file ID"})
		return
	}

	// Get file by ID
	file, err := api.filesRepo.GetConvertItemById(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get file", "details": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, file)
}
