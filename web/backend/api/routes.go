package api

import (
	"database/sql"
	"showfer-web/api/admin"
	"showfer-web/api/auth"
	"showfer-web/api/buckets"
	"showfer-web/api/convert_items"
	"showfer-web/api/files"
	"showfer-web/api/logs"
	"showfer-web/api/recorder"
	"showfer-web/api/schedule"
	"showfer-web/api/system"
	"showfer-web/api/websocket"
	"showfer-web/config"
	"showfer-web/middleware"
	"showfer-web/repository"
	"showfer-web/service/playout"
	"showfer-web/service/retranscoder"

	"github.com/gin-gonic/gin"
)

// SetupRoutes initializes all API routes
func SetupRoutes(router *gin.Engine, db *sql.DB, playout *playout.Playout, retranscoderService *retranscoder.RetranscoderService) {
	// API v1 group
	v1 := router.Group("/api/v1")
	{
		// Auth routes
		authAPI := auth.NewAuthAPI(db)
		authRoutes := v1.Group("/auth")
		{
			authRoutes.POST("/login", authAPI.Login)
			authRoutes.POST("/register", authAPI.Register)
		}

		// User routes (protected by auth middleware)
		userRoutes := v1.Group("/users")
		userRoutes.Use(middleware.AuthMiddleware())
		{
			userRoutes.GET("", middleware.RoleMiddleware("admin"), authAPI.GetUsers)
			userRoutes.GET("/me", authAPI.GetCurrentUser)
			userRoutes.GET("/:id", middleware.RoleMiddleware("admin"), authAPI.GetUser)
			userRoutes.POST("", middleware.RoleMiddleware("admin"), authAPI.CreateUser)
			userRoutes.PUT("/:id", middleware.RoleMiddleware("admin"), authAPI.UpdateUser)
			userRoutes.DELETE("/:id", middleware.RoleMiddleware("admin"), authAPI.DeleteUser)
		}

		// Recorder routes (protected by auth middleware)
		recorderAPI := recorder.NewRecorderAPI(db)
		recorderRoutes := v1.Group("/recorders")
		recorderRoutes.Use(middleware.AuthMiddleware())
		{
			recorderRoutes.GET("", recorderAPI.GetRecorders)
			recorderRoutes.GET("/:id", recorderAPI.GetRecorder)
			recorderRoutes.POST("", recorderAPI.CreateRecorder)
			recorderRoutes.PUT("/:id", recorderAPI.UpdateRecorder)
			recorderRoutes.DELETE("/:id", recorderAPI.DeleteRecorder)
			recorderRoutes.POST("/:id/start", recorderAPI.StartRecorder)
			recorderRoutes.POST("/:id/stop", recorderAPI.StopRecorder)
			recorderRoutes.POST("/:id/fail", recorderAPI.FailRecorder)
			recorderRoutes.POST("/:id/join", recorderAPI.JoinRecorder)
			recorderRoutes.POST("/:id/unjoin", recorderAPI.UnjoinRecorder)
			recorderRoutes.GET("/status", recorderAPI.GetRecorderStatus)
			recorderRoutes.GET("/:id/services", recorderAPI.GetRecorderServices) // New endpoint for getting services
		}

		// RTP URL routes (protected by auth middleware)
		rtpRoutes := v1.Group("/rtp-urls")
		rtpRoutes.Use(middleware.AuthMiddleware())
		{
			rtpRoutes.GET("", recorderAPI.GetRtpUrls)
			rtpRoutes.POST("/validate", recorderAPI.ValidateRtpURL)
		}

		// RTP Senders routes (protected by auth middleware)
		rtpSendersRoutes := v1.Group("/rtp-senders")
		rtpSendersRoutes.Use(middleware.AuthMiddleware())
		{
			rtpSendersRoutes.POST("", recorderAPI.GetRtpSenders)
		}

		// Network interface routes for recorder (protected by auth middleware)
		networkRoutes := v1.Group("/network")
		networkRoutes.Use(middleware.AuthMiddleware())
		{
			networkRoutes.GET("/interfaces", recorderAPI.GetNetworkInterfaces)
		}

		// File Manager routes (protected by auth middleware)
		filesAPI := files.NewFilesAPI(db, playout.BaseDir)
		fileStatusAPI := files.NewFileStatusAPI(repository.NewFilesRepository(db))
		filesRoutes := v1.Group("/files")
		filesRoutes.Use(middleware.AuthMiddleware())
		{
			filesRoutes.GET("", filesAPI.GetFiles)
			filesRoutes.GET("/location/:location", filesAPI.GetFilesByLocation)
			filesRoutes.GET("/folders/:location", filesAPI.GetFoldersByLocation)
			filesRoutes.POST("/upload", filesAPI.UploadFiles)
			filesRoutes.DELETE("/:id", filesAPI.DeleteFile)
			filesRoutes.GET("/:id", filesAPI.GetFileInfoById)
			filesRoutes.PUT("/:id", filesAPI.UpdateFile)
			filesRoutes.PUT("/:id/rename", filesAPI.RenameFile)
			filesRoutes.PUT("/:id/simple-rename", filesAPI.SimpleRename) // Debug endpoint
			filesRoutes.GET("/:id/preview-url", filesAPI.GetPreviewURL)
			filesRoutes.GET("/:id/test-update", filesAPI.TestMongoUpdate) // Debug endpoint
			filesRoutes.POST("/move", filesAPI.MoveFiles)
			filesRoutes.GET("/recorder/:id", fileStatusAPI.GetFilesByRecorderID)

			// Use the special download auth middleware for the download endpoint
			// This allows authentication via token query parameter
			downloadRoute := v1.Group("/files/:id/download")
			downloadRoute.Use(middleware.DownloadAuthMiddleware())
			downloadRoute.GET("", filesAPI.DownloadFile)
		}

		// System monitoring routes (protected by auth middleware)
		systemAPI := system.NewSystemAPI()
		systemRoutes := v1.Group("/system")
		systemRoutes.Use(middleware.AuthMiddleware())
		{
			systemRoutes.GET("/stats", systemAPI.GetSystemStats)
			systemRoutes.GET("/thresholds", systemAPI.GetAlarmThresholds)
			systemRoutes.PUT("/thresholds", middleware.RoleMiddleware("admin"), systemAPI.UpdateAlarmThresholds)
		}

		// Admin routes (protected by auth middleware and admin role)
		adminAPI := admin.NewAdminAPI(db)
		// Initialize retranscode API (memory-based)
		retranscodeAPI := admin.NewRetranscodeAPI(retranscoderService)
		adminRoutes := v1.Group("/admin")
		adminRoutes.Use(middleware.AuthMiddleware(), middleware.RoleMiddleware("admin"))
		{
			// User management
			adminRoutes.GET("/users/pending", adminAPI.GetPendingUsers)
			adminRoutes.POST("/users/:id/approve", adminAPI.ApproveUser)
			adminRoutes.POST("/users/:id/reject", adminAPI.RejectUser)

			// Network settings
			adminRoutes.GET("/network/interfaces", adminAPI.GetNetworkInterfaces)
			adminRoutes.PUT("/network/interfaces/:name", adminAPI.UpdateNetworkInterface)

			// Codec settings
			adminRoutes.GET("/codec-settings", adminAPI.GetCodecSettings)
			adminRoutes.PUT("/codec-settings", adminAPI.UpdateCodecSettings)

			adminRoutes.GET("/general-settings", adminAPI.GetGeneralSettings)
			adminRoutes.PUT("/general-settings", adminAPI.UpdateGeneralSettings)

			// Retranscoding routes
			adminRoutes.POST("/retranscode/start", retranscodeAPI.StartRetranscoding)
			adminRoutes.GET("/retranscode/status", retranscodeAPI.GetRetranscodeStatus)
			adminRoutes.GET("/retranscode/jobs", retranscodeAPI.GetRetranscodeJobs)
			adminRoutes.POST("/retranscode/activate", retranscodeAPI.ActivateNewCodecSettings)
		}

		// WebSocket routes (no auth for now, could be added with a custom middleware)
		wsHandler := websocket.NewWebSocketHandler()
		router.GET("/ws", wsHandler.HandleWebSocket)

		// Schedule routes
		scheduleAPI := schedule.NewScheduleApi(db, playout)
		scheduleRoutes := v1.Group("/schedule")
		{
			scheduleRoutes.GET("", scheduleAPI.GetSchedules)
			scheduleRoutes.POST("", scheduleAPI.CreateSchedule)
			scheduleRoutes.GET("/:id", scheduleAPI.GetSchedule)
			scheduleRoutes.PUT("/:id", scheduleAPI.UpdateSchedule)
			scheduleRoutes.DELETE("/:id", scheduleAPI.DeleteSchedule)
			scheduleRoutes.GET("/:id/guide", scheduleAPI.FindGuideByScheduleId)
			scheduleRoutes.POST("/:id/guide", scheduleAPI.UpdateGuide)
			scheduleRoutes.POST("/:id/guide/change-program", scheduleAPI.ChangeProgramInGuide)
			scheduleRoutes.GET("/files", scheduleAPI.FilesToNestedStructure)
			scheduleRoutes.POST("/files", scheduleAPI.BatchUpdateFiles)
			scheduleRoutes.GET("/status", scheduleAPI.GetSchedulersStatus)
			scheduleRoutes.GET("/epg", scheduleAPI.GetSchedulesForEPG)
		}

		// Playout routes
		playoutAPI := NewPlayoutAPI(playout)
		playoutRoutes := v1.Group("/playout")
		{
			playoutRoutes.GET("/preview/:shortID", playoutAPI.GetStreamPreviewURL)
		}

		// Analytics routes
		analyticsRepo := repository.NewAnalyticsRepository(db)
		analyticsAPI := NewAnalyticsAPI(analyticsRepo)
		analyticsAPI.RegisterRoutes(v1)

		// Logs routes (protected by auth middleware)
		logsAPI := logs.NewLogsAPI(db)
		logsRoutes := v1.Group("/logs")
		logsRoutes.Use(middleware.AuthMiddleware())
		{
			logsRoutes.GET("", logsAPI.GetLogs)
			logsRoutes.GET("/stats", logsAPI.GetLogStats)
			logsRoutes.GET("/sources", logsAPI.GetLogSources)
			logsRoutes.POST("/export", logsAPI.ExportLogs)
			logsRoutes.DELETE("/cleanup", middleware.RoleMiddleware("admin"), logsAPI.CleanupOldLogs)
		}

		// Bucket routes (protected by auth middleware)
		mongoDB := config.GetMongoDatabase()
		if mongoDB != nil {
			bucketsAPI := buckets.NewBucketsAPI(mongoDB)
			bucketRoutes := v1.Group("/buckets")
			bucketRoutes.Use(middleware.AuthMiddleware())
			{
				bucketRoutes.GET("", bucketsAPI.GetBuckets)
				bucketRoutes.GET("/test", bucketsAPI.TestConnection)
				bucketRoutes.GET("/default", bucketsAPI.GetDefaultBucket)
				bucketRoutes.GET("/user/:userId", bucketsAPI.GetBucketsByUser)
				bucketRoutes.GET("/:id", bucketsAPI.GetBucketByID)
				bucketRoutes.POST("", middleware.RoleMiddleware("admin"), bucketsAPI.CreateBucket)
				bucketRoutes.PUT("/:id", middleware.RoleMiddleware("admin"), bucketsAPI.UpdateBucket)
				bucketRoutes.DELETE("/:id", middleware.RoleMiddleware("admin"), bucketsAPI.DeleteBucket)
			}

			// Convert Items MongoDB routes (protected by auth middleware)
			convertItemsAPI := convert_items.NewConvertItemsMongoAPI()
			convertItemsRoutes := v1.Group("/convert-items")
			convertItemsRoutes.Use(middleware.AuthMiddleware())
			{
				convertItemsRoutes.GET("/test", convertItemsAPI.TestConnection)
				convertItemsRoutes.GET("/buckets", convertItemsAPI.GetBuckets)
				convertItemsRoutes.GET("/bucket/:bucketId", convertItemsAPI.GetItemsByBucket)
				convertItemsRoutes.POST("/by-bucket-and-location", convertItemsAPI.GetItemsByBucketAndLocation)
				convertItemsRoutes.POST("/by-bucket-subfolders", convertItemsAPI.GetSubfoldersByBucket)
				convertItemsRoutes.POST("/by-bucket-name", convertItemsAPI.GetItemsByBucketName)
				convertItemsRoutes.GET("/search", convertItemsAPI.SearchItems)
				convertItemsRoutes.GET("/:id", convertItemsAPI.GetItemByID)
				convertItemsRoutes.GET("", convertItemsAPI.GetAllItems)
			}
		}
	}

	// WebSocket route for logs (protected by WebSocket auth middleware)
	wsLogsRoute := router.Group("/ws/logs")
	wsLogsRoute.Use(middleware.WebSocketAuthMiddleware())
	{
		logsAPI := logs.NewLogsAPI(db)
		wsLogsRoute.GET("", logsAPI.HandleLogsWebSocket)
	}
}
