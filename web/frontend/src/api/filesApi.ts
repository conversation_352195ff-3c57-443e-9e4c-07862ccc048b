import api from './axios';
import { ConvertItem, ConvertItemListResult, ConvertItemUpdateInput } from '../types/files';

// Interface for move files result
interface MoveFilesResult {
  success: boolean;
  failedCount: number;
  errors: string[];
}

// Get files with pagination
export const getFiles = async (page: number = 1, limit: number = 50): Promise<ConvertItemListResult> => {
  const response = await api.get<ConvertItemListResult>(`/files?page=${page}&limit=${limit}`);
  return response.data;
};

// Get files by location
export const getFilesByLocation = async (location: string): Promise<ConvertItem[]> => {
  // Handle root location specially
  if (location === '/') {
    const response = await api.get<ConvertItem[]>('/files/location/root');
    return response.data;
  }

  // Remove leading and trailing slashes for the URL
  const cleanLocation = location.replace(/^\/|\/$/g, '');

  // Replace slashes with a different separator for the API
  // Make sure to handle empty segments correctly
  const encodedLocation = cleanLocation.split('/').filter(part => part !== '').map(part => encodeURIComponent(part)).join('_');

  const response = await api.get<ConvertItem[]>(`/files/location/${encodedLocation}`);
  return response.data;
};

// Get folders by location
export const getFoldersByLocation = async (location: string): Promise<string[]> => {
  // Handle root location specially
  if (location === '/') {
    const response = await api.get<string[]>('/files/folders/root');
    return response.data;
  }

  // Remove leading and trailing slashes for the URL
  const cleanLocation = location.replace(/^\/|\/$/g, '');

  // Replace slashes with a different separator for the API
  // Make sure to handle empty segments correctly
  const encodedLocation = cleanLocation.split('/').filter(part => part !== '').map(part => encodeURIComponent(part)).join('_');

  const response = await api.get<string[]>(`/files/folders/${encodedLocation}`);
  return response.data;
};

// Upload files with progress tracking
export const uploadFiles = async (
  files: File[],
  location: string,
  bucketName: string,
  onProgress?: (progress: number, loaded: number, total: number) => void
): Promise<{ message: string, files: ConvertItem[] }> => {
  const formData = new FormData();
  formData.append('location', location);
  formData.append('bucketName', bucketName);

  // Append each file to the form data
  for (const file of files) {
    formData.append('files', file);
  }

  const response = await api.post<{ message: string, files: ConvertItem[] }>('/files/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progressEvent) => {
      if (progressEvent.total) {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        if (onProgress) {
          onProgress(percentCompleted, progressEvent.loaded, progressEvent.total);
        }
      }
    }
  });

  return response.data;
};

// Delete a file
export const deleteFile = async (id: number): Promise<{ success: boolean; error?: string; retryable?: boolean }> => {
  try {
    await api.delete(`/files/${id}`);
    return { success: true };
  } catch (error: any) {
    // If the file is already deleted (404), we can consider this a success
    if (error.response && error.response.status === 404) {
      console.log(`File with ID ${id} already deleted or not found`);
      return { success: true }; // Return successfully even though it was a 404
    }

    // For 500 errors, check if it's a database lock issue
    if (error.response && error.response.status === 500) {
      const errorData = error.response.data;
      console.error(`Error deleting file with ID ${id}:`, errorData);

      // Check if it's a database lock error
      const isDbLockError =
        errorData?.details?.includes('database is locked') ||
        errorData?.details?.includes('SQLITE_BUSY');

      if (isDbLockError) {
        // This is a retryable error
        return {
          success: false,
          error: 'Database is locked, will retry',
          retryable: true
        };
      }

      // For other 500 errors, consider the operation successful for the UI
      // This is because the file might have been deleted from the database but not from disk
      return {
        success: true,
        error: errorData?.error || 'Server error while deleting file'
      };
    }

    // For other errors, return failure with error details
    return {
      success: false,
      error: error.response?.data?.error || error.message || 'Unknown error',
      retryable: false
    };
  }
};

// Delete multiple files
export const deleteMultipleFiles = async (ids: number[]): Promise<{ success: boolean; failedCount: number; errors: string[] }> => {
  // Since there's no bulk delete endpoint, we'll delete files one by one
  // Process files sequentially to avoid database locks
  const results = [];
  const errors: string[] = [];
  let failedCount = 0;

  // Process each file sequentially with retry logic
  for (const id of ids) {
    // Try up to 3 times with increasing delays
    let result = null;
    for (let attempt = 0; attempt < 3; attempt++) {
      result = await deleteFile(id);

      if (result.success) {
        // If successful, break out of retry loop
        break;
      } else if (!result.retryable) {
        // If error is not retryable, don't attempt again
        console.log(`Non-retryable error for file ID ${id}: ${result.error}`);
        break;
      } else {
        // If this is not the last attempt, wait before retrying
        if (attempt < 2) {
          // Exponential backoff: 500ms, then 1500ms
          const delay = 500 * Math.pow(3, attempt);
          await new Promise(resolve => setTimeout(resolve, delay));
          console.log(`Retrying delete for file ID ${id}, attempt ${attempt + 2}`);
        }
      }
    }

    results.push(result);

    if (result && !result.success) {
      failedCount++;
      errors.push(`Failed to delete file ID ${id}: ${result.error}`);
    } else if (result && result.error) {
      // Log server errors even for "successful" deletions
      console.warn(`Warning while deleting file ID ${id}: ${result.error}`);
    }

    // Add a small delay between file deletions to reduce contention
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // Return a summary of the operation
  return {
    success: failedCount === 0,
    failedCount,
    errors
  };
};

// Update a file's metadata
export const updateFile = async (id: number, data: ConvertItemUpdateInput): Promise<ConvertItem> => {
  const response = await api.put<ConvertItem>(`/files/${id}`, data);
  return response.data;
};

// Rename a file (changes the actual filename)
export const renameFile = async (id: number, newFilename: string): Promise<ConvertItem> => {
  const response = await api.put<ConvertItem>(`/files/${id}/rename`, { new_filename: newFilename });
  return response.data;
};

// Download a file
export const downloadFile = (id: number): void => {
  // Get the authentication token from localStorage
  const token = localStorage.getItem('token');

  // Get the base URL from the API client's configuration
  // This ensures we're using the same base URL as other API calls
  const baseURL = api.defaults.baseURL || '/api/v1';

  // Create the download URL using the same pattern as other API functions
  const downloadUrl = `${window.location.origin}${baseURL}/files/${id}/download`;

  // Create a temporary anchor element to trigger the download
  const link = document.createElement('a');

  // Add the token as a query parameter for authentication
  link.href = token ? `${downloadUrl}?token=${token}` : downloadUrl;
  link.setAttribute('download', ''); // This will use the server's suggested filename
  document.body.appendChild(link);
  link.click();

  // Clean up
  document.body.removeChild(link);
};

// Get files by recorder ID
export const getFilesByRecorderId = async (recorderId: number): Promise<ConvertItem[]> => {
  try {
    const response = await api.get<ConvertItem[]>(`/files/recorder/${recorderId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching files by recorder ID:', error);
    return [];
  }
};

// Move files to a different location
export const moveFiles = async (
  fileIds: number[],
  destinationLocation: string
): Promise<MoveFilesResult> => {
  try {
    const response = await api.post<MoveFilesResult>('/files/move', {
      file_ids: fileIds,
      destination: destinationLocation
    });
    return response.data;
  } catch (error: any) {
    console.error('Error moving files:', error);

    // Return a structured error response
    return {
      success: false,
      failedCount: fileIds.length,
      errors: [error.response?.data?.error || error.message || 'Unknown error moving files']
    };
  }
};
