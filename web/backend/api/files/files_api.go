package files

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"showfer-web/config"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/logger"
	"showfer-web/service/storage"
	"showfer-web/service/uploader"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// FilesAPI handles file-related API endpoints
type FilesAPI struct {
	db                *sql.DB
	filesRepo         *repository.FilesRepository
	codecSettingsRepo *repository.CodecSettingsRepository
	baseDir           string
}

// NewFilesAPI creates a new FilesAPI
func NewFilesAPI(db *sql.DB, baseDir string) *FilesAPI {
	// Create a data directory if it doesn't exist
	if _, err := os.Stat(baseDir); os.IsNotExist(err) {
		err = os.MkdirAll(baseDir, 0755)
		if err != nil {
			logger.Error("Failed to create data directory: %v", err)
			panic(err)
		}
	}

	return &FilesAPI{
		db:                db,
		filesRepo:         repository.NewFilesRepository(db),
		codecSettingsRepo: repository.NewCodecSettingsRepository(db),
		baseDir:           baseDir,
	}
}

// GetFiles handles GET /api/v1/files
func (api *FilesAPI) GetFiles(c *gin.Context) {
	// Parse pagination parameters
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.DefaultQuery("limit", "50"))
	if err != nil || limit < 1 || limit > 100 {
		limit = 50
	}

	pagination := models.Pagination{
		Page:  page,
		Limit: limit,
	}

	// Get files with pagination
	result, err := api.filesRepo.ListConvertItems(pagination)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get files", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetFilesByLocation handles GET /api/v1/files/location/:location
func (api *FilesAPI) GetFilesByLocation(c *gin.Context) {
	location := c.Param("location")

	// Handle special "root" location
	if location == "root" {
		location = "/"
	} else {
		// Replace underscores with slashes for nested paths
		location = strings.ReplaceAll(location, "_", "/")

		// Handle case where we might have double slashes
		for strings.Contains(location, "//") {
			location = strings.ReplaceAll(location, "//", "/")
		}

		// Ensure location starts with /
		if !strings.HasPrefix(location, "/") {
			location = "/" + location
		}

		// Ensure location ends with /
		if !strings.HasSuffix(location, "/") && location != "/" {
			location = location + "/"
		}
	}

	// Get files by location
	files, err := api.filesRepo.ConvertItemsByFolder(location)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get files by location", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, files)
}

// GetFoldersByLocation handles GET /api/v1/files/folders/:location
func (api *FilesAPI) GetFoldersByLocation(c *gin.Context) {
	location := c.Param("location")

	// Handle special "root" location
	if location == "root" {
		location = "/"
	} else {
		// Replace underscores with slashes for nested paths
		location = strings.ReplaceAll(location, "_", "/")

		// Handle case where we might have double slashes
		for strings.Contains(location, "//") {
			location = strings.ReplaceAll(location, "//", "/")
		}

		// Ensure location starts with /
		if !strings.HasPrefix(location, "/") {
			location = "/" + location
		}

		// Ensure location ends with /
		if !strings.HasSuffix(location, "/") && location != "/" {
			location = location + "/"
		}
	}

	// Get folders by location
	folders, err := api.filesRepo.GetSubfolders(location)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get folders by location", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, folders)
}

// UploadFiles handles POST /api/v1/files/upload
func (api *FilesAPI) UploadFiles(c *gin.Context) {
	// Get location from form
	log.Println("This is an info message##################################")
	location := c.PostForm("location")
	if location == "" {
		location = "/"
	}

	// Get bucket ID from form (required for Backblaze upload)
	bucketIdStr := c.PostForm("bucketId")
	log.Println("bucketId", bucketIdStr)
	if bucketIdStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Bucket ID is required"})
		return
	}

	// Convert bucket ID string to ObjectID
	bucketID, err := primitive.ObjectIDFromHex(bucketIdStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid bucket ID format", "details": err.Error()})
		return
	}

	// Ensure location starts with /
	if !strings.HasPrefix(location, "/") {
		location = "/" + location
	}

	// Ensure location ends with /
	if !strings.HasSuffix(location, "/") && location != "/" {
		location = location + "/"
	}

	// Create temporary folder for processing
	tempDir := filepath.Join(api.baseDir, "temp")
	if _, err := os.Stat(tempDir); os.IsNotExist(err) {
		err = os.MkdirAll(tempDir, 0755)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create temp folder", "details": err.Error()})
			return
		}
	}

	// Get file from form
	form, err := c.MultipartForm()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse form", "details": err.Error()})
		return
	}

	files := form.File["files"]
	if len(files) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No files uploaded"})
		return
	}

	// Get Backblaze credentials from config
	cfg, err := config.LoadBackblazeConfig()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to load Backblaze config", "details": err.Error()})
		return
	}

	// Get bucket information from MongoDB using bucket ID
	mongoDatabase := config.GetMongoDatabase()
	bucketRepo := repository.NewBucketRepository(mongoDatabase)
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	bucket, err := bucketRepo.GetBucketByID(ctx, bucketID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid bucket ID in database", "details": err.Error()})
		return
	}

	bucketName := bucket.BucketName

	// Log the bucket name for debugging
	logger.Log("MongoDB bucket name: '%s'", bucketName)

	// Normalize bucket name for Backblaze (replace spaces with hyphens)
	normalizedBucketName := strings.ReplaceAll(bucketName, " ", "-")
	logger.Log("Normalized bucket name for B2: '%s'", normalizedBucketName)

	// Initialize Backblaze uploader
	backblazeService, err := storage.NewBackblazeService(cfg.KeyID, cfg.AppKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to initialize Backblaze service", "details": err.Error()})
		return
	}

	// Verify bucket exists in Backblaze B2 before proceeding
	logger.Log("Verifying bucket '%s' exists in Backblaze B2...", normalizedBucketName)
	b2Buckets, err := backblazeService.ListBuckets()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list Backblaze buckets", "details": err.Error()})
		return
	}

	bucketExists := false
	for _, b2Bucket := range b2Buckets {
		if b2Bucket.Name == normalizedBucketName {
			bucketExists = true
			break
		}
	}

	if !bucketExists {
		logger.Error("Bucket '%s' not found in Backblaze B2. Available buckets: %v", normalizedBucketName, func() []string {
			var names []string
			for _, b := range b2Buckets {
				names = append(names, b.Name)
			}
			return names
		}())
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Bucket not found in Backblaze B2",
			"details": fmt.Sprintf("Original bucket name: '%s', Normalized for B2: '%s' does not exist in your Backblaze account", bucketName, normalizedBucketName),
			"available_buckets": func() []string {
				var names []string
				for _, b := range b2Buckets {
					names = append(names, b.Name)
				}
				return names
			}(),
		})
		return
	}

	backblazeUploader := uploader.NewBackblazeUploader(backblazeService)

	var uploadedFiles []models.ConvertItemResponse
	for _, file := range files {
		// Save file to temporary location first
		filename := filepath.Base(file.Filename)
		tempFilePath := filepath.Join(tempDir, filename)

		// Check if file already exists in temp
		if _, err := os.Stat(tempFilePath); err == nil {
			// File exists, append timestamp to filename
			ext := filepath.Ext(filename)
			baseName := filename[:len(filename)-len(ext)]
			timestamp := time.Now().Format("**************")
			filename = fmt.Sprintf("%s_%s%s", baseName, timestamp, ext)
			tempFilePath = filepath.Join(tempDir, filename)
		}

		// Save file to temp location
		if err := c.SaveUploadedFile(file, tempFilePath); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save file", "details": err.Error()})
			return
		}

		// Process file and upload to Backblaze
		convertItem, err := backblazeUploader.ProcessFileToBackblaze(tempFilePath, location, normalizedBucketName, bucket.ID)
		if err != nil {
			// Log error but continue with other files
			logger.Error("Failed to process file %s: %v", filename, err)
			// Clean up temp file
			os.Remove(tempFilePath)
			continue
		}

		uploadedFiles = append(uploadedFiles, convertItem.ToResponse())
	}

	c.JSON(http.StatusOK, gin.H{
		"message": fmt.Sprintf("Successfully uploaded %d files", len(uploadedFiles)),
		"files":   uploadedFiles,
	})
}

// DeleteFile handles DELETE /api/v1/files/:id
func (api *FilesAPI) DeleteFile(c *gin.Context) {
	// Parse file ID as MongoDB ObjectID
	fileIdStr := c.Param("id")
	fileID, err := primitive.ObjectIDFromHex(fileIdStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file ID format", "details": err.Error()})
		return
	}

	// Get MongoDB repository
	mongoRepo := repository.NewMongoConvertItemsRepository()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Get file by ID from MongoDB
	file, err := mongoRepo.GetConvertItemByIDMongo(ctx, fileID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "File not found in database", "details": err.Error()})
		return
	}

	logger.Log("=== DELETE FILE DEBUG ===")
	logger.Log("Deleting file: %s", file.FileName)
	logger.Log("Backblaze File ID: %s", file.BackblazeFileID)
	logger.Log("Backblaze URL: %s", file.BackblazeURL)

	// Get bucket information to determine bucket name for Backblaze deletion
	mongoDatabase := config.GetMongoDatabase()
	bucketRepo := repository.NewBucketRepository(mongoDatabase)

	var bucketName string
	if len(file.Bucket) > 0 {
		bucket, err := bucketRepo.GetBucketByID(ctx, file.Bucket[0])
		if err != nil {
			logger.Error("Failed to get bucket info: %v", err)
			bucketName = "default" // fallback
		} else {
			bucketName = strings.ReplaceAll(bucket.BucketName, " ", "-") // normalize for B2
		}
	}

	// Initialize Backblaze service
	cfg, err := config.LoadBackblazeConfig()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to load Backblaze config", "details": err.Error()})
		return
	}

	backblazeService, err := storage.NewBackblazeService(cfg.KeyID, cfg.AppKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to initialize Backblaze service", "details": err.Error()})
		return
	}

	// Delete file from Backblaze B2 if BackblazeFileID exists
	if file.BackblazeFileID != "" && bucketName != "" {
		err = backblazeService.DeleteFile(bucketName, file.BackblazeFileID)
		if err != nil {
			logger.Error("Failed to delete file from Backblaze B2: %v", err)
			// Continue with database deletion even if B2 deletion fails
		} else {
			logger.Log("✅ File deleted from Backblaze B2: %s", file.BackblazeFileID)
		}
	}

	// Delete file from MongoDB using the collection directly
	mongoDatabase = config.GetMongoDatabase()
	collection := mongoDatabase.Collection("convert_items")
	_, err = collection.DeleteOne(ctx, bson.M{"_id": fileID})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete file from database", "details": err.Error()})
		return
	}

	logger.Log("✅ File deleted from MongoDB: %s", fileID.Hex())
	logger.Log("=== DELETE COMPLETE ===")

	c.JSON(http.StatusOK, gin.H{"message": "File deleted successfully"})
}

// GetPreviewURL handles GET /api/v1/files/:id/preview-url
func (api *FilesAPI) GetPreviewURL(c *gin.Context) {
	// Parse file ID as MongoDB ObjectID
	fileIdStr := c.Param("id")
	fileID, err := primitive.ObjectIDFromHex(fileIdStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file ID format", "details": err.Error()})
		return
	}

	// Get MongoDB repository
	mongoRepo := repository.NewMongoConvertItemsRepository()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Get file by ID from MongoDB
	file, err := mongoRepo.GetConvertItemByIDMongo(ctx, fileID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "File not found in database", "details": err.Error()})
		return
	}

	logger.Log("=== PREVIEW URL DEBUG ===")
	logger.Log("Getting preview URL for file: %s", file.FileName)
	logger.Log("File location: %s", file.Location)

	// Get bucket information to construct Backblaze URL
	if len(file.Bucket) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File has no bucket information"})
		return
	}

	mongoDatabase := config.GetMongoDatabase()
	bucketRepo := repository.NewBucketRepository(mongoDatabase)
	bucket, err := bucketRepo.GetBucketByID(ctx, file.Bucket[0])
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get bucket info", "details": err.Error()})
		return
	}

	// Normalize bucket name for Backblaze (replace spaces with hyphens)
	normalizedBucketName := strings.ReplaceAll(bucket.BucketName, " ", "-")

	// Get Backblaze credentials
	cfg, err := config.LoadBackblazeConfig()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to load Backblaze config", "details": err.Error()})
		return
	}

	// Initialize Backblaze service
	backblazeService, err := storage.NewBackblazeService(cfg.KeyID, cfg.AppKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to initialize Backblaze service", "details": err.Error()})
		return
	}

	// Construct file path in Backblaze: location + filename
	// Remove leading slash from location if present
	cleanLocation := strings.TrimPrefix(file.Location, "/")
	var filePath string
	if cleanLocation == "" {
		filePath = file.FileName
	} else {
		filePath = cleanLocation + "/" + file.FileName
	}

	logger.Log("Bucket: %s (normalized: %s)", bucket.BucketName, normalizedBucketName)
	logger.Log("File path in bucket: %s", filePath)

	// Get download URL from Backblaze
	downloadURL, err := backblazeService.GetFileURL(normalizedBucketName, filePath)
	if err != nil {
		logger.Error("Failed to get Backblaze download URL: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get download URL", "details": err.Error()})
		return
	}

	logger.Log("✅ Generated preview URL: %s", downloadURL)
	logger.Log("=== PREVIEW URL COMPLETE ===")

	c.JSON(http.StatusOK, gin.H{
		"previewUrl": downloadURL,
		"filename":   file.FileName,
		"bucket":     bucket.BucketName,
		"location":   file.Location,
	})
}

// UpdateFile handles PUT /api/v1/files/:id
func (api *FilesAPI) UpdateFile(c *gin.Context) {
	// Parse file ID
	log.Println("This is an info message##################################")

	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file ID"})
		return
	}

	// Parse request body
	var input models.ConvertItemUpdateInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Get file by ID
	file, err := api.filesRepo.GetConvertItemById(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get file", "details": err.Error()})
		}
		return
	}

	// Update file
	file.Name = input.Name
	file.Description = input.Description
	file.Episode = input.Episode

	err = api.filesRepo.UpdateConvertItem(file)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update file", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, file)
}

// RenameFile handles PUT /api/v1/files/:id/rename
func (api *FilesAPI) RenameFile(c *gin.Context) {
	// Parse file ID as MongoDB ObjectID
	fileIdStr := c.Param("id")
	fileID, err := primitive.ObjectIDFromHex(fileIdStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file ID format", "details": err.Error()})
		return
	}

	// Parse request body
	var input struct {
		NewFilename string `json:"new_filename"`
	}
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Validate new filename
	if input.NewFilename == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "New filename cannot be empty"})
		return
	}

	// Get MongoDB repository
	mongoRepo := repository.NewMongoConvertItemsRepository()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Get file by ID from MongoDB
	file, err := mongoRepo.GetConvertItemByIDMongo(ctx, fileID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "File not found in database", "details": err.Error()})
		return
	}

	logger.Log("=== RENAME FILE DEBUG ===")
	logger.Log("Renaming file: %s -> %s", file.FileName, input.NewFilename)
	logger.Log("File location: %s", file.Location)

	// Get bucket information to rename file in Backblaze
	if len(file.Bucket) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "File has no bucket information"})
		return
	}

	mongoDatabase := config.GetMongoDatabase()
	bucketRepo := repository.NewBucketRepository(mongoDatabase)
	bucket, err := bucketRepo.GetBucketByID(ctx, file.Bucket[0])
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get bucket info", "details": err.Error()})
		return
	}

	// Normalize bucket name for Backblaze (replace spaces with hyphens)
	normalizedBucketName := strings.ReplaceAll(bucket.BucketName, " ", "-")

	// Construct old and new file paths in Backblaze
	cleanLocation := strings.TrimPrefix(file.Location, "/")
	var oldFilePath, newFilePath string

	if cleanLocation == "" {
		oldFilePath = file.FileName
		newFilePath = input.NewFilename
	} else {
		oldFilePath = cleanLocation + "/" + file.FileName
		newFilePath = cleanLocation + "/" + input.NewFilename
	}

	logger.Log("Bucket: %s (normalized: %s)", bucket.BucketName, normalizedBucketName)
	logger.Log("Old file path: %s", oldFilePath)
	logger.Log("New file path: %s", newFilePath)

	// Initialize Backblaze service and rename file
	cfg, err := config.LoadBackblazeConfig()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to load Backblaze config", "details": err.Error()})
		return
	}

	backblazeService, err := storage.NewBackblazeService(cfg.KeyID, cfg.AppKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to initialize Backblaze service", "details": err.Error()})
		return
	}

	// Rename file in Backblaze B2
	err = backblazeService.RenameFile(normalizedBucketName, oldFilePath, newFilePath)
	if err != nil {
		logger.Error("Failed to rename file in Backblaze B2: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to rename file in cloud storage", "details": err.Error()})
		return
	}

	logger.Log("✅ File renamed successfully in Backblaze B2")

	// Update filename in MongoDB
	collection := mongoDatabase.Collection("convert_items")

	update := bson.M{
		"$set": bson.M{
			"fileName":  input.NewFilename,
			"name":      strings.TrimSuffix(input.NewFilename, filepath.Ext(input.NewFilename)),
			"updatedAt": time.Now(),
		},
	}

	_, err = collection.UpdateOne(ctx, bson.M{"_id": fileID}, update)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update filename in database", "details": err.Error()})
		return
	}

	// Get updated file
	updatedFile, err := mongoRepo.GetConvertItemByIDMongo(ctx, fileID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get updated file", "details": err.Error()})
		return
	}

	logger.Log("✅ File renamed successfully in MongoDB")
	logger.Log("=== RENAME COMPLETE ===")

	c.JSON(http.StatusOK, updatedFile.ToResponse())
}

// DownloadFile handles GET /api/v1/files/:id/download
func (api *FilesAPI) DownloadFile(c *gin.Context) {
	// Parse file ID as MongoDB ObjectID
	fileIdStr := c.Param("id")
	fileID, err := primitive.ObjectIDFromHex(fileIdStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file ID format", "details": err.Error()})
		return
	}

	// Get MongoDB repository
	mongoRepo := repository.NewMongoConvertItemsRepository()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Get file by ID from MongoDB
	file, err := mongoRepo.GetConvertItemByIDMongo(ctx, fileID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "File not found in database", "details": err.Error()})
		return
	}

	logger.Log("=== DOWNLOAD FILE DEBUG ===")
	logger.Log("Downloading file: %s", file.FileName)
	logger.Log("Backblaze URL: %s", file.BackblazeURL)

	// For Backblaze B2 files, redirect to the Backblaze URL
	if file.BackblazeURL != "" {
		logger.Log("✅ Redirecting to Backblaze URL")
		c.Redirect(http.StatusTemporaryRedirect, file.BackblazeURL)
		return
	}

	// Fallback: if no Backblaze URL, return error
	logger.Error("No Backblaze URL found for file: %s", file.FileName)
	c.JSON(http.StatusNotFound, gin.H{
		"error":   "File download URL not available",
		"details": "This file was not properly uploaded to cloud storage",
	})
}

// MoveFiles handles POST /api/v1/files/move
func (api *FilesAPI) MoveFiles(c *gin.Context) {
	// Parse request body
	var input struct {
		FileIDs     []int64 `json:"file_ids"`
		Destination string  `json:"destination"`
	}
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Validate input
	if len(input.FileIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No file IDs provided"})
		return
	}

	// Ensure destination starts with /
	if !strings.HasPrefix(input.Destination, "/") {
		input.Destination = "/" + input.Destination
	}

	// Ensure destination ends with /
	if !strings.HasSuffix(input.Destination, "/") && input.Destination != "/" {
		input.Destination = input.Destination + "/"
	}

	// Create destination folder if it doesn't exist
	destPath := filepath.Join(api.baseDir, input.Destination)
	if _, err := os.Stat(destPath); os.IsNotExist(err) {
		err = os.MkdirAll(destPath, 0755)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create destination folder", "details": err.Error()})
			return
		}
	}

	// Process each file
	failedCount := 0
	errors := []string{}
	successCount := 0

	for _, fileID := range input.FileIDs {
		// Get file by ID
		file, err := api.filesRepo.GetConvertItemById(fileID)
		if err != nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("Failed to get file with ID %d: %v", fileID, err))
			continue
		}

		// Skip files that are being processed
		if file.Status == int(models.FileStatusQueue) || file.Status == int(models.FileStatusProcessing) {
			failedCount++
			errors = append(errors, fmt.Sprintf("File %s is currently being processed and cannot be moved", file.Filename))
			continue
		}

		// Check if source and destination are the same
		if file.Location == input.Destination {
			failedCount++
			errors = append(errors, fmt.Sprintf("File %s is already in the destination folder", file.Filename))
			continue
		}

		// Check if a file with the same name already exists in the destination
		destFilePath := filepath.Join(destPath, file.Filename)
		if _, err := os.Stat(destFilePath); err == nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("A file with the name %s already exists in the destination folder", file.Filename))
			continue
		}

		// Move file on disk
		sourcePath := filepath.Join(api.baseDir, file.Location, file.Filename)
		err = os.Rename(sourcePath, destFilePath)
		if err != nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("Failed to move file %s: %v", file.Filename, err))
			continue
		}

		// Update file location in database
		oldLocation := file.Location

		// Use the new UpdateFileLocation function to update both location and c_location
		// and also update any references in schedules
		err = api.filesRepo.UpdateFileLocation(file.ID, oldLocation, input.Destination)
		if err != nil {
			// Try to move the file back to its original location
			os.Rename(destFilePath, sourcePath)

			failedCount++
			errors = append(errors, fmt.Sprintf("Failed to update file %s in database: %v", file.Filename, err))
			continue
		}

		// Update the in-memory file object to reflect the new location
		file.Location = input.Destination
		file.CLocation = input.Destination

		successCount++
		logger.Log("Moved file %s from %s to %s", file.Filename, oldLocation, input.Destination)
	}

	c.JSON(http.StatusOK, gin.H{
		"success":      failedCount == 0,
		"failedCount":  failedCount,
		"successCount": successCount,
		"errors":       errors,
	})
}

// GetFileInfoById handles GET /api/v1/files/:id
func (api *FilesAPI) GetFileInfoById(c *gin.Context) {
	// Parse file ID
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file ID"})
		return
	}

	// Get file by ID
	file, err := api.filesRepo.GetConvertItemById(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get file", "details": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, file)
}
