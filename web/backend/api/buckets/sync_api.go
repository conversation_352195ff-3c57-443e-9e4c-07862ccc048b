package buckets

import (
	"context"
	"fmt"
	"net/http"
	"showfer-web/config"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/logger"
	"showfer-web/service/storage"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// SyncAPI handles bucket synchronization between MongoDB and Backblaze B2
type SyncAPI struct {
	bucketRepo *repository.BucketRepository
}

// NewSyncAPI creates a new SyncAPI instance
func NewSyncAPI() *SyncAPI {
	mongoDatabase := config.GetMongoDatabase()
	return &SyncAPI{
		bucketRepo: repository.NewBucketRepository(mongoDatabase),
	}
}

// SyncBucketsResponse represents the response from bucket sync operation
type SyncBucketsResponse struct {
	Success          bool                 `json:"success"`
	Message          string               `json:"message"`
	BackblazeBuckets []storage.BucketInfo `json:"backblaze_buckets"`
	MongoBuckets     []models.Bucket      `json:"mongo_buckets"`
	CreatedBuckets   []models.Bucket      `json:"created_buckets,omitempty"`
	MissingInB2      []models.Bucket      `json:"missing_in_b2,omitempty"`
	Error            string               `json:"error,omitempty"`
}

// ListBackblazeBuckets handles GET /api/v1/buckets/sync/list-b2
func (api *SyncAPI) ListBackblazeBuckets(c *gin.Context) {
	// Get Backblaze credentials from config
	cfg, err := config.LoadBackblazeConfig()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to load Backblaze config", "details": err.Error()})
		return
	}

	// Initialize Backblaze service
	backblazeService, err := storage.NewBackblazeService(cfg.KeyID, cfg.AppKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to initialize Backblaze service", "details": err.Error()})
		return
	}

	// List buckets from Backblaze B2
	b2Buckets, err := backblazeService.ListBuckets()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list Backblaze buckets", "details": err.Error()})
		return
	}

	// Get MongoDB buckets
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	mongoBuckets, err := api.bucketRepo.GetAllBuckets(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get MongoDB buckets", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, SyncBucketsResponse{
		Success:          true,
		Message:          "Successfully retrieved bucket information",
		BackblazeBuckets: b2Buckets,
		MongoBuckets:     mongoBuckets,
	})
}

// SyncBuckets handles POST /api/v1/buckets/sync
func (api *SyncAPI) SyncBuckets(c *gin.Context) {
	// Get Backblaze credentials from config
	cfg, err := config.LoadBackblazeConfig()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to load Backblaze config", "details": err.Error()})
		return
	}

	// Initialize Backblaze service
	backblazeService, err := storage.NewBackblazeService(cfg.KeyID, cfg.AppKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to initialize Backblaze service", "details": err.Error()})
		return
	}

	// List buckets from Backblaze B2
	b2Buckets, err := backblazeService.ListBuckets()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list Backblaze buckets", "details": err.Error()})
		return
	}

	// Get MongoDB buckets
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	mongoBuckets, err := api.bucketRepo.GetAllBuckets(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get MongoDB buckets", "details": err.Error()})
		return
	}

	// Create a map of existing MongoDB bucket names for quick lookup
	mongoMap := make(map[string]models.Bucket)
	for _, bucket := range mongoBuckets {
		mongoMap[bucket.BucketName] = bucket
	}

	// Create a map of Backblaze bucket names for validation
	b2Map := make(map[string]storage.BucketInfo)
	for _, bucket := range b2Buckets {
		b2Map[bucket.Name] = bucket
	}

	var createdBuckets []models.Bucket
	var missingInB2 []models.Bucket

	// Check which MongoDB buckets are missing in Backblaze
	for _, mongoBucket := range mongoBuckets {
		if _, exists := b2Map[mongoBucket.BucketName]; !exists {
			missingInB2 = append(missingInB2, mongoBucket)
		}
	}

	// Create MongoDB entries for Backblaze buckets that don't exist in MongoDB
	for _, b2Bucket := range b2Buckets {
		if _, exists := mongoMap[b2Bucket.Name]; !exists {
			// Create new bucket in MongoDB
			bucketInput := models.BucketCreateInput{
				Title:      b2Bucket.Name, // Use bucket name as title
				BucketName: b2Bucket.Name,
				IsShowAll:  true,
				Default:    len(mongoBuckets) == 0, // Set as default if it's the first bucket
				User:       []primitive.ObjectID{}, // Empty user array
			}

			newBucket, err := api.bucketRepo.CreateBucket(ctx, bucketInput)
			if err != nil {
				logger.Error("Failed to create bucket %s in MongoDB: %v", b2Bucket.Name, err)
				continue
			}

			createdBuckets = append(createdBuckets, *newBucket)
			logger.Log("Created bucket '%s' in MongoDB", b2Bucket.Name)
		}
	}

	// Prepare response
	response := SyncBucketsResponse{
		Success:          true,
		Message:          "Bucket synchronization completed",
		BackblazeBuckets: b2Buckets,
		MongoBuckets:     mongoBuckets,
		CreatedBuckets:   createdBuckets,
		MissingInB2:      missingInB2,
	}

	if len(missingInB2) > 0 {
		response.Message += ". Warning: Some MongoDB buckets don't exist in Backblaze B2"
	}

	c.JSON(http.StatusOK, response)
}

// DeleteInvalidBuckets handles DELETE /api/v1/buckets/sync/invalid
func (api *SyncAPI) DeleteInvalidBuckets(c *gin.Context) {
	// Get Backblaze credentials from config
	cfg, err := config.LoadBackblazeConfig()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to load Backblaze config", "details": err.Error()})
		return
	}

	// Initialize Backblaze service
	backblazeService, err := storage.NewBackblazeService(cfg.KeyID, cfg.AppKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to initialize Backblaze service", "details": err.Error()})
		return
	}

	// List buckets from Backblaze B2
	b2Buckets, err := backblazeService.ListBuckets()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list Backblaze buckets", "details": err.Error()})
		return
	}

	// Create a map of Backblaze bucket names for validation
	b2Map := make(map[string]bool)
	for _, bucket := range b2Buckets {
		b2Map[bucket.Name] = true
	}

	// Get MongoDB buckets
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	mongoBuckets, err := api.bucketRepo.GetAllBuckets(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get MongoDB buckets", "details": err.Error()})
		return
	}

	var deletedBuckets []models.Bucket
	var errors []string

	// Delete MongoDB buckets that don't exist in Backblaze
	for _, mongoBucket := range mongoBuckets {
		if !b2Map[mongoBucket.BucketName] {
			err := api.bucketRepo.DeleteBucket(ctx, mongoBucket.ID)
			if err != nil {
				errors = append(errors, "Failed to delete bucket "+mongoBucket.BucketName+": "+err.Error())
				logger.Error("Failed to delete invalid bucket %s: %v", mongoBucket.BucketName, err)
			} else {
				deletedBuckets = append(deletedBuckets, mongoBucket)
				logger.Log("Deleted invalid bucket '%s' from MongoDB", mongoBucket.BucketName)
			}
		}
	}

	response := gin.H{
		"success":         len(errors) == 0,
		"message":         "Invalid bucket cleanup completed",
		"deleted_buckets": deletedBuckets,
	}

	if len(errors) > 0 {
		response["errors"] = errors
	}

	c.JSON(http.StatusOK, response)
}

// NormalizeBucketNames handles POST /api/v1/buckets/sync/normalize
func (api *SyncAPI) NormalizeBucketNames(c *gin.Context) {
	// Get Backblaze credentials from config
	cfg, err := config.LoadBackblazeConfig()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to load Backblaze config", "details": err.Error()})
		return
	}

	// Initialize Backblaze service
	backblazeService, err := storage.NewBackblazeService(cfg.KeyID, cfg.AppKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to initialize Backblaze service", "details": err.Error()})
		return
	}

	// List buckets from Backblaze B2
	b2Buckets, err := backblazeService.ListBuckets()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list Backblaze buckets", "details": err.Error()})
		return
	}

	// Create a map of normalized names to actual B2 bucket names
	b2Map := make(map[string]string) // normalized -> actual
	for _, bucket := range b2Buckets {
		normalized := strings.ReplaceAll(bucket.Name, "-", " ")
		b2Map[normalized] = bucket.Name
	}

	// Get MongoDB buckets
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	mongoBuckets, err := api.bucketRepo.GetAllBuckets(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get MongoDB buckets", "details": err.Error()})
		return
	}

	var updatedBuckets []models.Bucket
	var errors []string

	// Update MongoDB bucket names to match Backblaze
	for _, mongoBucket := range mongoBuckets {
		if actualB2Name, exists := b2Map[mongoBucket.BucketName]; exists {
			// Update the bucket name in MongoDB to match Backblaze
			updateInput := models.BucketUpdateInput{
				BucketName: &actualB2Name,
			}

			updatedBucket, err := api.bucketRepo.UpdateBucket(ctx, mongoBucket.ID, updateInput)
			if err != nil {
				errors = append(errors, fmt.Sprintf("Failed to update bucket %s: %v", mongoBucket.BucketName, err))
				logger.Error("Failed to normalize bucket name %s: %v", mongoBucket.BucketName, err)
			} else {
				updatedBuckets = append(updatedBuckets, *updatedBucket)
				logger.Log("Normalized bucket name '%s' -> '%s'", mongoBucket.BucketName, actualB2Name)
			}
		}
	}

	response := gin.H{
		"success":         len(errors) == 0,
		"message":         "Bucket name normalization completed",
		"updated_buckets": updatedBuckets,
		"b2_buckets":      b2Buckets,
	}

	if len(errors) > 0 {
		response["errors"] = errors
	}

	c.JSON(http.StatusOK, response)
}
