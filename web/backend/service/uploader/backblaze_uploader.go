package uploader

import (
	"context"
	"os"
	"path/filepath"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/converter"
	"showfer-web/service/logger"
	"showfer-web/service/storage"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// BackblazeUploader handles file uploads to Backblaze B2 and saves metadata to MongoDB
type BackblazeUploader struct {
	backblazeService *storage.BackblazeService
	mongoRepo        *repository.MongoConvertItemsRepository
	bucketRepo       *repository.BucketRepository
}

// NewBackblazeUploader creates a new BackblazeUploader
func NewBackblazeUploader(backblazeService *storage.BackblazeService) *BackblazeUploader {
	return &BackblazeUploader{
		backblazeService: backblazeService,
		mongoRepo:        repository.NewMongoConvertItemsRepository(),
		bucketRepo:       nil, // Will be set when needed
	}
}

// ProcessFileToBackblaze processes a file upload to Backblaze B2 and saves metadata to MongoDB
func (bu *BackblazeUploader) ProcessFileToBackblaze(filePath, location, bucketName string, bucketID primitive.ObjectID) (*models.ConvertItemMongo, error) {
	filename := filepath.Base(filePath)

	// Check if file exists
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		logger.Error("Failed to get file info: %v", err)
		return nil, err
	}

	// Get file duration
	duration, err := converter.GetDuration(filePath)
	if err != nil {
		logger.Error("Failed to get file duration: %v", err)
		// Continue with duration 0
		duration = 0
	}

	// Get video and audio codec information
	videoCodec, audioCodec, err := getCodecInfo(filePath)
	if err != nil {
		logger.Error("Failed to get codec info: %v", err)
		// Use default values
		videoCodec = "h264"
		audioCodec = "aac"
	}

	// Upload file to Backblaze B2
	logger.Log("=== UPLOAD DEBUG ===")
	logger.Log("Uploading file: %s", filename)
	logger.Log("To Backblaze B2 bucket: %s", bucketName)
	logger.Log("File path: %s", filePath)
	logger.Log("Location: %s", location)
	logger.Log("File size: %d bytes", fileInfo.Size())

	fileInfoB2, err := bu.backblazeService.UploadFile(bucketName, filePath, location)
	if err != nil {
		logger.Error("Failed to upload file to Backblaze B2: %v", err)
		return nil, err
	}

	logger.Log("✅ Backblaze upload successful!")
	logger.Log("Backblaze File ID: %s", fileInfoB2.ID)
	logger.Log("Backblaze URL: %s", fileInfoB2.URL)

	// Create convert item for MongoDB
	now := time.Now()
	convertItem := models.ConvertItemMongo{
		ID:                    primitive.NewObjectID(),
		Logs:                  []string{"[" + now.Format("2006-01-02 15:04:05") + "] Start of process"},
		FileName:              filename,
		Status:                "queue", // Set status to queue initially for transcoding
		Location:              location,
		Storage:               []primitive.ObjectID{}, // Will be populated if needed
		Version:               0,
		CreatedAt:             now,
		UpdatedAt:             now,
		Filler:                false,
		Duration:              int(duration),
		Name:                  strings.TrimSuffix(filename, filepath.Ext(filename)),
		CFilename:             strings.TrimSuffix(filename, filepath.Ext(filename)),
		CK:                    "k",
		CLocation:             location,
		IsSix:                 true,
		Description:           "",
		Epnum:                 "",
		ID1:                   "",
		ID2:                   "",
		FTP:                   false,
		MigrateStatus:         0,
		Bucket:                []primitive.ObjectID{bucketID},
		IsS3:                  true, // Using cloud storage
		AnalyseLog:            []string{},
		ResolutionsForConvert: []string{},
		NeedsReconvert:        false,
		// New fields
		Size:            fileInfo.Size(),
		VideoCodec:      videoCodec,
		AudioCodec:      audioCodec,
		BackblazeFileID: fileInfoB2.ID,
		BackblazeURL:    fileInfoB2.URL,
	}

	// Save to MongoDB
	logger.Log("=== MONGODB SAVE DEBUG ===")
	logger.Log("Saving to MongoDB collection: convert_items")
	logger.Log("Bucket ID: %s", bucketID.Hex())
	logger.Log("Status: %s", convertItem.Status)
	logger.Log("Duration: %d", convertItem.Duration)
	logger.Log("Size: %d", convertItem.Size)

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	createdItem, err := bu.mongoRepo.CreateConvertItem(ctx, convertItem)
	if err != nil {
		logger.Error("❌ Failed to create convert item in MongoDB: %v", err)
		return nil, err
	}

	logger.Log("✅ MongoDB save successful!")
	logger.Log("File processed successfully: %s", filename)
	logger.Log("MongoDB Document ID: %s", createdItem.ID.Hex())
	logger.Log("Collection: convert_items")
	logger.Log("=== UPLOAD COMPLETE ===")

	// Delete local file after successful upload to Backblaze
	err = os.Remove(filePath)
	if err != nil {
		logger.Error("Failed to delete local file after upload: %v", err)
		// Don't return error as the upload was successful
	} else {
		logger.Log("Local file deleted after successful upload: %s", filePath)
	}

	return createdItem, nil
}

// getCodecInfo extracts video and audio codec information from a file
func getCodecInfo(filePath string) (string, string, error) {
	// Use ffprobe to get codec information
	videoCodec, err := converter.GetVideoCodec(filePath)
	if err != nil {
		videoCodec = "h264" // Default fallback
	}

	audioCodec, err := converter.GetAudioCodec(filePath)
	if err != nil {
		audioCodec = "aac" // Default fallback
	}

	return normalizeVideoCodecName(videoCodec), normalizeAudioCodecName(audioCodec), nil
}
