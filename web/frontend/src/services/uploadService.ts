import { store } from "../redux/store";
import {
  startUpload,
  updateFileProgress,
  updateFileStatus,
  completeUpload,
  failUpload,
  setShowModal,
  UploadFile,
} from "../redux/uploadSlice";
import { uploadFiles as apiUploadFiles } from "../api/filesApi";
import { toast } from "react-toastify";

class UploadService {
  private generateFileId(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }

  async uploadFiles(
    files: FileList | File[],
    location: string,
    bucketName: string,
    showModal: boolean = false
  ): Promise<void> {
    if (!files || files.length === 0) return;

    const fileArray = Array.isArray(files) ? files : Array.from(files);

    // Create upload file objects
    const uploadFileData: UploadFile[] = fileArray.map((file) => ({
      id: this.generateFileId(),
      name: file.name,
      size: file.size,
      progress: 0,
      status: "queued" as const,
      location: location,
    }));

    // Start upload in Redux store
    store.dispatch(startUpload({ files: uploadFileData, showModal }));

    // Track upload speed
    let lastLoaded = 0;
    let lastTime = Date.now();
    let currentFileIndex = 0;

    try {
      await apiUploadFiles(
        fileArray,
        location,
        bucketName,
        (progress: number, loaded: number, _total: number) => {
          // Update current file progress
          if (uploadFileData[currentFileIndex]) {
            store.dispatch(
              updateFileProgress({
                fileId: uploadFileData[currentFileIndex].id,
                progress: progress,
              })
            );

            // Update status to uploading if not already
            if (uploadFileData[currentFileIndex].status === "queued") {
              store.dispatch(
                updateFileStatus({
                  fileId: uploadFileData[currentFileIndex].id,
                  status: "uploading",
                })
              );
            }
          }

          // Calculate upload speed
          const now = Date.now();
          const timeElapsed = (now - lastTime) / 1000; // in seconds

          if (timeElapsed > 0.5) {
            // Update every 500ms
            const loadedSinceLastUpdate = loaded - lastLoaded;
            const bytesPerSecond = loadedSinceLastUpdate / timeElapsed;

            // Format speed as KB/s or MB/s
            let speedText = "";
            if (bytesPerSecond < 1024 * 1024) {
              speedText = `${(bytesPerSecond / 1024).toFixed(1)} KB/s`;
            } else {
              speedText = `${(bytesPerSecond / (1024 * 1024)).toFixed(1)} MB/s`;
            }

            // Update upload speed for current file
            if (uploadFileData[currentFileIndex]) {
              store.dispatch(
                updateFileProgress({
                  fileId: uploadFileData[currentFileIndex].id,
                  progress: progress,
                  speed: speedText,
                })
              );
            }

            lastLoaded = loaded;
            lastTime = now;
          }

          // If current file is complete, move to next file
          if (progress === 100 && uploadFileData[currentFileIndex]) {
            store.dispatch(
              updateFileStatus({
                fileId: uploadFileData[currentFileIndex].id,
                status: "completed",
              })
            );
            currentFileIndex++;
          }
        }
      );

      // Mark all files as completed
      uploadFileData.forEach((file) => {
        store.dispatch(
          updateFileStatus({
            fileId: file.id,
            status: "completed",
          })
        );
      });

      store.dispatch(completeUpload());
      toast.success(`Successfully uploaded ${fileArray.length} file(s)`);

      // Auto-hide modal after 3 seconds for completed uploads (only if it was showing)
      setTimeout(() => {
        const state = store.getState();
        if (!state.upload.isUploading && state.upload.showModal) {
          store.dispatch(setShowModal(false));
        }

        // Clear localStorage after upload completion and modal auto-hide
        setTimeout(() => {
          const finalState = store.getState();
          if (
            !finalState.upload.isUploading &&
            !finalState.upload.showModal &&
            !finalState.upload.showStatusNotification
          ) {
            // Only clear if user has dismissed everything
            // This gives them time to see the completed status
          }
        }, 5000); // Additional delay before clearing
      }, 3000);
    } catch (error) {
      console.error("Error uploading files:", error);
      store.dispatch(
        failUpload({
          error: error instanceof Error ? error.message : "Upload failed",
        })
      );
      toast.error("Failed to upload files");
      throw error;
    }
  }

  // Helper method to check if uploads are in progress
  isUploading(): boolean {
    return store.getState().upload.isUploading;
  }

  // Helper method to get upload progress
  getUploadProgress(): number {
    return store.getState().upload.totalProgress;
  }

  // Helper method to get current files
  getUploadFiles(): UploadFile[] {
    return store.getState().upload.files;
  }
}

// Export a singleton instance
export const uploadService = new UploadService();
export default uploadService;
